const {
    ethers
} = require("ethers");

const connection4 = require('../config/redis_config')(4);

const {
    Que<PERSON>,
    Worker,
} = require('bullmq');

const mysqlConfig = require('../config/mysqlConfig');
const axios = require('axios');

const knex = require('knex')({
    client: 'mysql',
    connection: {
        host: mysqlConfig.host,
        user: mysqlConfig.user,
        port: mysqlConfig.port,
        password: mysqlConfig.password,
        database: mysqlConfig.database
    }
});

const {
    url
} = require("../config/rpcUrl_config");

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

async function start() {
    while (true) {
        try {
            await sleep(5000);
            // 查询未发送的交易记录
            let transactions = await knex('transactions_all').where({
                sended: 0
            });

            // 处理每条未发送的记录
            for (let tx of transactions) {
                try {
                    // 构建请求数据
                    const postData = {
                        chainId: tx.chainId,
                        amount: tx.amount.toString(),
                        address: tx.address,
                        txid: tx.txid,
                        label: tx.label,
                        currency: tx.currency,
                        date: tx.date,
                        blockNumber: tx.blockNumber
                    };

                    // 发送请求
                    if (tx.ipn_url) {
                        const response = await axios.post(tx.ipn_url, postData, {
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            timeout: 10000 // 10秒超时
                        });

                        if (response.status === 200 && response.data === 'ok') {
                            // 更新发送状态
                            await knex('transactions_all')
                                .where({ id: tx.id })
                                .update({ sended: 1 });
                            console.log(`Transaction ${tx.txid} notification sent successfully`);
                        } else {
                            console.warn(`Transaction ${tx.txid} notification received non-ok response: ${JSON.stringify(response.data)}`);
                        }
                    }
                } catch (error) {
                    console.error(`Error processing transaction ${tx.txid}:`, error.message);
                    // 这里可以添加重试逻辑或记录失败日志
                }
            }
        } catch (error) {
            console.error('Main loop error:', error);
        }
    }
}

start();

const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.log(`process.on ${type}`)
        try {
            process.exit(0);
        } catch (err) {
            process.exit(1);
        }
    })
});