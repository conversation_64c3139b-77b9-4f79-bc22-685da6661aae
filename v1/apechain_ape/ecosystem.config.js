const {
  calcPath,
  getEnvVariables
} = require('./helpers');

module.exports = {
  apps: [

    {
      name: "v1/apechain-ape/4_ape_getblock2",
      cwd: calcPath('src/workers'),
      script: "4_ape_getblock2.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/apechain-ape/4_update_ape_balance2",
      cwd: calcPath('src/workers'),
      script: "4_update_ape_balance2.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/apechain-ape/checkDataWorker_ape",
      cwd: calcPath('src/workers'),
      script: "checkDataWorker_ape.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/apechain-ape/getBlockWithTransactionsWorker_ape",
      cwd: calcPath('src/workers'),
      script: "getBlockWithTransactionsWorker_ape.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },
    {
      name: "v1/apechain-ape/ui",
      cwd: calcPath('src'),
      script: "ui_apechain_ape.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },
    {
      name: "v1/apechain-ape/sendTxs",
      cwd: calcPath('src/workers'),
      script: "sendTxs.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      autorestart: true,
      max_restarts: 10,
      restart_delay: 5000,
      error_file: "logs/sendTxs-error.log",
      args: [],
      env: getEnvVariables()
    }

  ]
}