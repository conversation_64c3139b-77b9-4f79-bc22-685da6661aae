const connection10 = require('../config/redis_config')(10);

const moment = require('moment');

const {
    QueuePro,
    WorkerPro,
    QueueSchedulerPro
} = require('@taskforcesh/bullmq-pro');


const {
    ethers, constants
} = require("ethers");

const {
    rpcUrl
} = require("../config/rpcUrl_config");

const provider = new ethers.providers.StaticJsonRpcProvider(rpcUrl);

const QueueName = "updateBep20UsdtBalanceQueuePro";

const mysqlConfig = require('../config/mysqlConfig');

const knex = require('knex')({
    client: 'mysql',
    connection: {
        host: mysqlConfig.host,
        user: mysqlConfig.user,
        port: mysqlConfig.port,
        password: mysqlConfig.password,
        database: mysqlConfig.database
    }
});

const worker = new WorkerPro(QueueName, async (job) => {

    let address = job.data.address;
    let table = job.data.table;
    let field = job.data.field;
    const contractAddress = job.data.contractAddress;
    const abi = job.data.abi;
    const decimals = job.data.decimals;

    let bep20 = new ethers.Contract(contractAddress, abi, provider);

    let balanceCoin = await bep20.balanceOf(address);
    balance = ethers.utils.formatUnits(String(balanceCoin), decimals);
    if (balance) {
        console.log(address, balance,table);

        if(table == 'w_bep20_address'){
            let resss = await knex(table).update({
                [field]: balance,
                ck_conletion:0
            }).where({
                address: address
            });
    
            return resss;
        }else{
            let resss = await knex(table).update({
                [field]: balance,
            }).where({
                address: address
            });
    
            return resss;
        }
        
    }
}, {
    connection: connection10,
    concurrency: 1
});


worker.on("completed", (job, result) => {
    // console.log(`Completed job on queue ${QueueName}`, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("failed", (job, err) => {
    console.log(`Faille job on queue ${QueueName}`, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),job.data);
});

worker.on("error", (err) => {
    console.log(`error job on queue ${QueueName}`, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('paused', (job) => {
    console.error(`paused job on queue ${QueueName}`, job, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('resumed', (job) => {
    console.error(`resumed job on queue ${QueueName}`, job, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('closed', () => {
    console.error(`worker ${QueueName} 已退出`);
});

const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            console.error(`process.on ${type}`, err, origin);
            await worker.close();
            process.exit(0)
        } catch (_) {
            process.exit(1)
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.error('process.once', type);
        try {
            await worker.close();
        } finally {
            process.kill(process.pid, type)
        }
    })
});