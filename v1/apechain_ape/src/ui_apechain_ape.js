const { createBullBoard } = require('@bull-board/api');
const { BullMQAdapter } = require('@bull-board/api/bullMQAdapter');
const { ExpressAdapter } = require('@bull-board/express');
const { Queue: QueueMQ, Worker, QueueScheduler } = require('bullmq');
const session = require('express-session');
const bodyParser = require('body-parser');
const passport = require('passport');
const LocalStrategy = require('passport-local').Strategy;
const { ensureLoggedIn } = require('connect-ensure-login');
const express = require('express');

// Configure the local strategy for use by Passport.
//
// The local strategy require a `verify` function which receives the credentials
// (`username` and `password`) submitted by the user.  The function must verify
// that the password is correct and then invoke `cb` with a user object, which
// will be set at `req.user` in route handlers after authentication.
passport.use(
  new LocalStrategy(function (username, password, cb) {
    if (username === 'trc2ousdt' && password === '0329zouyongY') {
      return cb(null, { user: 'trc20-board' });
    }
    return cb(null, false);
  })
);

// Configure Passport authenticated session persistence.
//
// In order to restore authentication state across HTTP requests, Passport needs
// to serialize users into and deserialize users out of the session.  The
// typical implementation of this is as simple as supplying the user ID when
// serializing, and querying the user record by ID from the database when
// deserializing.
passport.serializeUser((user, cb) => {
  cb(null, user);
});

passport.deserializeUser((user, cb) => {
  cb(null, user);
});

const sleep = (t) => new Promise((resolve) => setTimeout(resolve, t * 1000));


const redisConfig = require('./config/redis_config')(4);



const createQueueMQ = (name) => new QueueMQ(name, { connection: redisConfig });

// async function setupBullMQProcessor(queueName) {
//   const queueScheduler = new QueueScheduler(queueName, {
//     connection: redisOptions,
//   });
//   await queueScheduler.waitUntilReady();

//   new Worker(queueName, async (job) => {
//     for (let i = 0; i <= 100; i++) {
//       await sleep(Math.random());
//       await job.updateProgress(i);
//       await job.log(`Processing job at interval ${i}`);

//       if (Math.random() * 200 < 1) throw new Error(`Random error ${i}`);
//     }

//     return { jobId: `This is the return value of job (${job.id})` };
//   });
// }

const run = async () => {

  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath('/ui');

  
const {
    addQueue,
    removeQueue,
    setQueues,
    replaceQueues
} = createBullBoard({
    queues: [


        new BullMQAdapter(new QueueMQ('updateApeBalanceQueuePro', {
            connection: redisConfig
        })),

        new BullMQAdapter(new QueueMQ('Ape_workerGetBlockWithTransactions', {
            connection: redisConfig
        })),

        new BullMQAdapter(new QueueMQ('checkData_db3_APE', {
            connection: redisConfig
        })),

        // new BullMQAdapter(new QueueMQ('checkData_db', {
        //     connection: redisConfig
        // })),

        // new BullMQAdapter(new QueueMQ('checkData_db2', {
        //     connection: redisConfig
        // }))

    
    ],
    serverAdapter: serverAdapter,
});

//   await setupBullMQProcessor(exampleBullMq.name);

  const app = express();
  // Configure view engine to render EJS templates.
  app.set('views', __dirname + '/views');
  app.set('view engine', 'ejs');

  app.use(session({ secret: 'keyboard cat', saveUninitialized: true, resave: true }));
  app.use(bodyParser.urlencoded({ extended: false }));

  // Initialize Passport and restore authentication state, if any, from the session.
  app.use(passport.initialize({}));
  app.use(passport.session({}));

  app.get('/ui/login', (req, res) => {
    res.render('login', { invalid: req.query.invalid === 'true' });
  });

  app.post(
    '/ui/login',
    passport.authenticate('local', { failureRedirect: '/ui/login?invalid=true' }),
    (req, res) => {
      res.redirect('/ui');
    }
  );


  app.use('/ui', ensureLoggedIn({ redirectTo: '/ui/login' }), serverAdapter.getRouter());

  app.listen(8914, () => {
    console.log('Running on 8914...');
    console.log('For the UI, open http://localhost:8914/ui');
  });
};

// eslint-disable-next-line no-console
run().catch((e) => console.error(e));