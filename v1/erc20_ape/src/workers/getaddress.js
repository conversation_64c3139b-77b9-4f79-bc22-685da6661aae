const {
    ethers
} = require("ethers");

const moment = require('moment');

const Redis = require("ioredis");



const {
    QueuePro,
    WorkerPro
} = require('@taskforcesh/bullmq-pro');

// const connection19 = require('../config/redis_config')(19);

const connection19 = require('../config/redis_config')(9);

const updateErc20ApeBalanceQueue = new QueuePro(`updateErc20ApeBalanceQueueProTmp`, {
    connection: connection19
});


const redis11 = new Redis(connection19);

const mysqlConfig = require('../config/mysqlConfig');

const knex = require('knex')({
    client: 'mysql',
    connection: {
        host: mysqlConfig.host,
        user: mysqlConfig.user,
        port: mysqlConfig.port,
        password: mysqlConfig.password,
        database: mysqlConfig.database
    }
});



const worker = new WorkerPro(`addr-track`, async (job) => {

    let address_erc20_ape_lastId = await redis11.get('address_erc20_ape_lastId');
    if (address_erc20_ape_lastId) {
        let addrs = await knex.column('id', 'address', 'ipn_url', 'label').from('address_erc20_ape').where('id', '>', address_erc20_ape_lastId).select().limit(100);
        for (let i = 0; i < addrs.length; i++) {
            await updateErc20ApeBalanceQueue.add('apeErc20',{
                address: addrs[i].address,
                field: 'erc20-ape',
                table: 'address_all',
                length:addrs.length
            });
            await redis11.set('address_erc20_ape_lastId', addrs[i].id);
        }
    } else {
        await redis11.set('address_erc20_ape_lastId', 1);
    }

}, {
    connection: connection19
});


worker.on("completed", (job, result) => {
    // console.log(`Completed job on queue trc20_block_track`, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("failed", (job, err) => {
    console.log(`Faille job on queue `, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("error", (err) => {
    console.log(`error job on queue `, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});


worker.on('closed', () => {
    console.error(`worker  已退出`);
});


const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            console.error(`process.on ${type}`, err, origin);
            await worker.close();
            process.exit(0)
        } catch (_) {
            process.exit(1)
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.error('process.once', type);
        try {
            await worker.close();
        } finally {
            process.kill(process.pid, type)
        }
    })
});