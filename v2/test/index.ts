const sharp = require('sharp');
const fs = require('fs');

// 需要压缩的图片路径
const imagePath = 'input.jpg';

// 压缩后的图片保存路径
const outputPath = 'output.jpg';

// 压缩质量，范围为 1-100，默认值为80
const quality = 60;

// 使用 sharp 对图片进行压缩
sharp(imagePath).jpeg({ quality: quality }).toFile(outputPath, (err, info) => {
  if (err) {
    console.error(err);
  } else {
    console.log(`Output file size: ${Math.round(info.size / 1024)}KB`);
  }
});
