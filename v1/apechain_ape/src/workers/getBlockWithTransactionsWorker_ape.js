const {
    ethers
} = require("ethers");

const connection4 = require('../config/redis_config')(4);

const moment = require('moment');
const {
    Queue,
    Worker
} = require('bullmq');

const {
    url
} = require("../config/rpcUrl_config");


const provider = new ethers.providers.StaticJsonRpcProvider(url);

// let block = 0;

// provider.once('block', async (data) => {

//     block = data;
// });



const checkData_dbQueue = new Queue('checkData_db3_APE', {
    connection: {
        ...connection4
    },
    removeOnComplete: 100,
    removeOnFail: 100
});

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// const { createPublicClient, http } = require('viem');

// const { apeChain } = require('viem/chains');

// const client = createPublicClient({
//     chain: apeChain,
//     transport: http()
// })


const workerGetBlockWithTransactionsWorker = new Worker("Ape_workerGetBlockWithTransactions", async (job) => {

    const num = job.data.num;

    // console.log('33333333', num);


    // try {
    //     let list = await provider.getBlockWithTransactions(num);

    //     console.log(list);
    // } catch (error) {
    //     console.log(error);

    // }


    // return;

    try {

        // let list = await client.getBlock({
        //     blockNumber: num,
        //     includeTransactions: true
        // });
        let list = await provider.getBlockWithTransactions(Number(num));

        // console.log(list.transactions.length);

        // return;

        let arr = [];
        for (let i = 0; i < list.transactions.length; i++) {

            if (list.transactions[i].from) {
                arr.push({
                    name: `address:${list.transactions[i].from}`,
                    data: {
                        hash: list.transactions[i].hash,
                        from: list.transactions[i].from,
                        to: list.transactions[i].to,
                        value: list.transactions[i].value.toString(),
                        blockNumber: list.transactions[i].blockNumber.toString()
                    },
                    opts: {
                        attempts: 10,
                        backoff: {
                            type: 'exponential',
                            delay: 1000,
                        },
                        removeOnFail: 1000,
                        removeOnComplete: 2000
                    }

                });
            }
        }
        if (arr.length > 0) {
            await checkData_dbQueue.addBulk(arr);
        }

    } catch (error) {

        console.error(error);


    }

    // console.log('list', list.length, num);



}, {
    connection: {
        ...connection4
    },
    concurrency: 26
});


workerGetBlockWithTransactionsWorker.on("completed", (job, result) => {
    // console.log(`Completed job on queue workerGetBlockWithTransactions`, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

workerGetBlockWithTransactionsWorker.on("failed", (job, err) => {
    console.log(`Faille job on queue Ape_workerGetBlockWithTransactions`, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

workerGetBlockWithTransactionsWorker.on("error", (err) => {
    console.log(`error job on queue Ape_workerGetBlockWithTransactions`, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.log(`process.on ${type}`)
        try {

            await workerGetBlockWithTransactionsWorker.close();
            await redis.quit();
            process.exit(0);
        } catch (err) {
            process.exit(1);
        }
    })
});