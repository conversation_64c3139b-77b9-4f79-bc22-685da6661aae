const {
    ethers
} = require("ethers");

const fs = require("fs");

const moment = require('moment');

const {
    QueuePro,
    WorkerPro
} = require('@taskforcesh/bullmq-pro');

// const connection19 = require('../config/redis_config')(19);

const connection19 = require('../config/redis_config')(9);



const addr_track_Queue = new QueuePro(`addr-track`, {
    connection: connection19
});

async function main() {
    
    // await addr_track_Queue.removeRepeatableByKey('repeatAddr::::2000');

    // addr_track_Queue.add('repeatAddr', {}, {
    //     repeat: {
    //         every: 2000,
    //     },
    //     attempts: 10,
    //     backoff: {
    //         type: 'exponential',
    //         delay: 1000,
    //     },
    //     removeOnComplete: 1000,
    //     removeOnFail: 1000
    // });
}

main();


const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            console.error(`process.on ${type}`, err, origin);
            await addr_track_Queue.close();
            process.exit(0)
        } catch (_) {
            process.exit(1)
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.error('process.once', type);
        try {
            await addr_track_Queue.close();
        } finally {
            process.kill(process.pid, type)
        }
    })
});