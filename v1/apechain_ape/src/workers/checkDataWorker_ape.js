const {
    ethers
} = require("ethers");

const connection4 = require('../config/redis_config')(4);

const connection8 = require('../config/redis_config')(8);

const connection6 = require('../config/redis_config')(6);

const moment = require('moment');
const {
    Queue,
    Worker,
} = require('bullmq');

const {
    url
} = require("../config/rpcUrl_config");

const { createPublicClient, http } = require('viem');

const { apeChain } = require('viem/chains');

const client = createPublicClient({
    chain: apeChain,
    transport: http()
})

const provider = new ethers.providers.StaticJsonRpcProvider(url);

const Redis = require("ioredis");
const redis = new Redis({
    ...connection4,
    maxRetriesPerRequest: null
});

const redis8 = new Redis({
    ...connection8,
    maxRetriesPerRequest: null
});


const redis6 = new Redis({
    ...connection6,
    maxRetriesPerRequest: null
});



const updateBnbBalanceQueueQueue = new Queue('updateApeBalanceQueuePro', {
    connection: redis
});

const mysqlConfig = require('../config/mysqlConfig');

const knex = require('knex')({
    client: 'mysql',
    connection: {
        host: mysqlConfig.host,
        user: mysqlConfig.user,
        port: mysqlConfig.port,
        password: mysqlConfig.password,
        database: mysqlConfig.database
    }
});

const checkData_dbWork = new Worker("checkData_db3_APE", async (job) => {

    try {

        let {
            hash,
            from,
            to,
            value,
            blockNumber
        } = job.data;        

        if (to) {

            if (to == '******************************************') {
                return;
            }
            value = ethers.BigNumber.from(value);
            from = from.toLowerCase();
            to = to.toLowerCase();

            // value = ethers.utils.formatUnits(value, 18);

            // console.log('value', value, blockNumber);

            // if(value>0){
            //     const res = await client.getTransactionReceipt({
            //         hash
            //     });
            //     console.log(res);
            // }


            const tohas = await redis6.get(`chain_eth_ape_${to}`);
            if (tohas) {

                value = ethers.utils.formatUnits(value, 18);

                let toInfo = JSON.parse(tohas);

                await updateBnbBalanceQueueQueue.add(to, {
                    address: to,
                    addressTable: toInfo.addressTable,
                    field: toInfo.field
                }, {
                    attempts: 10,
                    backoff: {
                        type: 'exponential',
                        delay: 1000,
                    },
                    removeOnFail: 1000,
                    removeOnComplete: 2000
                });


                let rep = await provider.getTransactionReceipt(hash);

                // const rep = await client.getTransactionReceipt({
                //     hash
                // });


                if (rep && rep.status == true) {
                    let find = await knex(toInfo.hashTable).where({
                        txid: hash
                    });
                    if (find.length == 0) {
                        await knex(toInfo.hashTable).insert({
                            chainId: 'ape',
                            sender: from,
                            address: to,
                            amount: value,
                            txid: hash,
                            ipn_url: toInfo.ipn_url,
                            label: toInfo.label,
                            currency: 'ape',
                            blockNumber: blockNumber,
                        });
                    } else {
                        console.log(`系统已存在ape`, hash);
                    }
                }
            }


            // let fromhas = await redis8.get(`eth_${from}`);

            const fromhas = await redis6.get(`chain_eth_ape_${from}`);


            if (fromhas) {
                value = ethers.utils.formatUnits(value, 18);

                let fromInfo = JSON.parse(fromhas);

                console.log('got from', fromInfo);

                await updateBnbBalanceQueueQueue.add(from, {
                    address: from,
                    addressTable: fromInfo.addressTable,
                    field: fromInfo.field
                }, {
                    attempts: 10,
                    backoff: {
                        type: 'exponential',
                        delay: 1000,
                    },
                    removeOnFail: 1000,
                    removeOnComplete: 2000
                });


            }
        }

    } catch (error) {
        console.error(error);
    }


}, {
    connection: redis,
    concurrency: 100
});




checkData_dbWork.on("completed", (job, result) => {
    // console.log(`Completed job on queue checkData_dbWork`, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

checkData_dbWork.on("failed", (job, err) => {
    console.log(`Faille job on queue checkData_db3_APE`, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

checkData_dbWork.on("error", (err) => {
    console.log(`error job on queue checkData_db3_APE`, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.log(`process.on ${type}`)
        try {



            await checkData_dbWork.close();
            await checkData_dbScheduler.close();
            await redis.quit();

            process.exit(0);
        } catch (err) {
            process.exit(1);
        }
    })
});