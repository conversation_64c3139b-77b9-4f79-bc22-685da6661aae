const {
    ethers
} = require("ethers");
const {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>
} = require('@taskforcesh/bullmq-pro');

const Redis = require("ioredis");

const moment = require('moment');

const yong_usdt_abi = [{ "inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "owner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "spender", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "value", "type": "uint256" }], "name": "Approval", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "previousOwner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "newOwner", "type": "address" }], "name": "OwnershipTransferred", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "from", "type": "address" }, { "indexed": true, "internalType": "address", "name": "to", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "value", "type": "uint256" }], "name": "Transfer", "type": "event" }, { "constant": true, "inputs": [], "name": "_decimals", "outputs": [{ "internalType": "uint8", "name": "", "type": "uint8" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "_name", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "_symbol", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }, { "internalType": "address", "name": "spender", "type": "address" }], "name": "allowance", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [{ "internalType": "address", "name": "spender", "type": "address" }, { "internalType": "uint256", "name": "amount", "type": "uint256" }], "name": "approve", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [{ "internalType": "address", "name": "account", "type": "address" }], "name": "balanceOf", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [{ "internalType": "uint256", "name": "amount", "type": "uint256" }], "name": "burn", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "decimals", "outputs": [{ "internalType": "uint8", "name": "", "type": "uint8" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [{ "internalType": "address", "name": "spender", "type": "address" }, { "internalType": "uint256", "name": "subtractedValue", "type": "uint256" }], "name": "decreaseAllowance", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "getOwner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [{ "internalType": "address", "name": "spender", "type": "address" }, { "internalType": "uint256", "name": "addedValue", "type": "uint256" }], "name": "increaseAllowance", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [{ "internalType": "uint256", "name": "amount", "type": "uint256" }], "name": "mint", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "name", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "owner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [], "name": "renounceOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": true, "inputs": [], "name": "symbol", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": true, "inputs": [], "name": "totalSupply", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "payable": false, "stateMutability": "view", "type": "function" }, { "constant": false, "inputs": [{ "internalType": "address", "name": "recipient", "type": "address" }, { "internalType": "uint256", "name": "amount", "type": "uint256" }], "name": "transfer", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [{ "internalType": "address", "name": "sender", "type": "address" }, { "internalType": "address", "name": "recipient", "type": "address" }, { "internalType": "uint256", "name": "amount", "type": "uint256" }], "name": "transferFrom", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "payable": false, "stateMutability": "nonpayable", "type": "function" }, { "constant": false, "inputs": [{ "internalType": "address", "name": "newOwner", "type": "address" }], "name": "transferOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function" }]


const {
    rpcUrl
} = require("../config/rpcUrl_config");

const provider = new ethers.providers.StaticJsonRpcProvider(rpcUrl);


const connection10 = require('../config/redis_config')(10);

const redisConfig6 = require('../config/redis_config')(6);

const redisConfig8 = require('../config/redis_config')(8);


const redis8 = new Redis(redisConfig8);

const mysqlConfig = require('../config/mysqlConfig');

const redis6 = new Redis(redisConfig6);


const redisConfig5 = require('../config/redis_config')(5);

const knex = require('knex')({
    client: 'mysql',
    connection: {
        host: mysqlConfig.host,
        user: mysqlConfig.user,
        port: mysqlConfig.port,
        password: mysqlConfig.password,
        database: mysqlConfig.database
    }
});


const transferQueue = new QueuePro('queue_bep20_usdt_transfer', {
    connection: redisConfig5
});

const updateBep20UsdtBalanceQueuePro = new QueuePro('updateBep20UsdtBalanceQueuePro', {
    connection: connection10
});

const checkLogs_bep20_usdtQueuePro = new QueuePro('checkLogs-bep20-usdt', {
    connection: connection10
});


const worker = new WorkerPro('checkLogs-bep20-usdt', async (job) => {

    const data = job.data;
    const toAddress = (data.to).toLowerCase();
    const fromAddress = (data.from).toLowerCase();
    const amount = Number(data.value);
    const chain = data.chain;
    const chainID = data.chainID;
    const toHas = await redis6.get(`chainAll_${toAddress}`);
    const fromHas = await redis6.get(`chainAll_${fromAddress}`);


    let yongToHas = await redis6.get(`bep20misaddr_${(toAddress).toLowerCase()}`);
    let yongfromHas = await redis6.get(`bep20misaddr_${(fromAddress).toLowerCase()}`);

    let towithdraw = await redis8.get(`bep20_usdt_senderAddress_${toAddress}`);
    let fromwithdraw = await redis8.get(`bep20_usdt_senderAddress_${fromAddress}`);


    if (yongfromHas) {
        await updateBep20UsdtBalanceQueuePro.add(`address:from:${data.transactionHash}`, {
            side: 'OUT',
            amount: amount,
            hash: data.transactionHash,
            address: fromAddress,
            table: 'w_bep20_address',
            field: 'usdt',
            chainID: 'bsc',
            decimals: 18,
            contractAddress: '0x55d398326f99059fF775485246999027B3197955',
            abi: yong_usdt_abi,

        }, {
            attempts: 20,
            backoff: {
                type: 'exponential',
                delay: 1000,
            }
        });
    }

    if(yongToHas){

        await updateBep20UsdtBalanceQueuePro.add(`address:from:${data.transactionHash}`, {
            side: 'IN',
            amount: amount,
            hash: data.transactionHash,
            address: toAddress,
            table: 'w_bep20_address',
            field: 'usdt',
            chainID: 'bsc',
            decimals: 18,
            contractAddress: '0x55d398326f99059fF775485246999027B3197955',
            abi: yong_usdt_abi,

        }, {
            attempts: 20,
            backoff: {
                type: 'exponential',
                delay: 1000,
            }
        });

        if (amount > 0) {

            let receipt = await provider.getTransactionReceipt(data.transactionHash);

            if (receipt && receipt.status == true) {

                const toInfo = JSON.parse(yongToHas);

                let find = await knex(toInfo.hashTable).where({
                    txid: data.transactionHash
                });
                if (find.length == 0) {
                    await knex(toInfo.hashTable).insert({
                        chainId: 'bsc',
                        sender: fromAddress,
                        address: toAddress,
                        amount: amount,
                        txid: data.transactionHash,
                        ipn_url: toInfo.ipn_url,
                        label: toInfo.label,
                        currency: 'bep20-usdt',
                        blockNumber: data.blockNumber
                    });
                } else {
                    console.log(`系统已存在bsc`, data.transactionHash);
                }
            } else {
                console.error('未确认，重新执行', data.transactionHash);
                await checkLogs_bep20_usdtQueuePro.add(`${data.transactionHash}`, job.data, {
                    attempts: 10,
                    backoff: {
                        type: 'exponential',
                        delay: 1000,
                    },
                    removeOnFail: 1000,
                    removeOnComplete: 2000
                });
            }
        } else {
            // console.error('不存...', job.data);
        }
    }


    if (fromHas) {

        // if (amount > 0) {
        await updateBep20UsdtBalanceQueuePro.add(`address:from:${data.transactionHash}`, {
            side: 'OUT',
            amount: amount,
            hash: data.transactionHash,
            address: fromAddress,
            table: 'address_all',
            field: 'bep20-usdt',
            chainID: chainID,
            decimals: data.decimals,
            contractAddress: data.contractAddress,
            abi: data.abi,

        }, {
            attempts: 20,
            backoff: {
                type: 'exponential',
                delay: 1000,
            }
        });
        // }


    }
    if (fromwithdraw) {
        await updateBep20UsdtBalanceQueuePro.add(`address:${data.transactionHash}`, {
            address: fromAddress,
            table: 'bep20_usdt_send_address',
            field: 'usdt',
            chainID: chainID,
            decimals: data.decimals,
            contractAddress: data.contractAddress,
            abi: data.abi,
            hash: data.transactionHash
        }, {
            attempts: 20,
            backoff: {
                type: 'exponential',
                delay: 1000,
            }
        });
    }


    if (towithdraw) {

        await updateBep20UsdtBalanceQueuePro.add(`address:${data.transactionHash}`, {
            address: toAddress,
            table: 'bep20_usdt_send_address',
            field: 'usdt',
            chainID: chainID,
            decimals: data.decimals,
            contractAddress: data.contractAddress,
            abi: data.abi,
            hash: data.transactionHash
        }, {
            attempts: 20,
            backoff: {
                type: 'exponential',
                delay: 1000,
            }
        });

        //

        let receipt = await provider.getTransactionReceipt(data.transactionHash);

        if (receipt && receipt.status) {

            //重启队列
            //todo
            try {
                const ress = await transferQueue.resumeGroup(toAddress);
                console.log('重启....', toAddress, ress);
            } catch (error) {
                console.error(error);
            }

            const toInfo = JSON.parse(towithdraw);

            let find = await knex(toInfo.hashTable).where({
                txid: data.transactionHash
            });
            if (find.length == 0) {
                await knex(toInfo.hashTable).insert({
                    chainId: chainID,
                    sender: fromAddress,
                    address: toAddress,
                    amount: amount,
                    txid: data.transactionHash,
                    ipn_url: toInfo.ipn_url,
                    label: toInfo.label,
                    currency: chain,
                    blockNumber: data.blockNumber,
                    ...data.other
                });
            } else {
                console.log(`系统已存在${job.data.chain}`, data.transactionHash);
            }
        }

    }


    if (toHas) {

        await updateBep20UsdtBalanceQueuePro.add(`address:to:${data.transactionHash}`, {
            hash: data.transactionHash,
            side: 'IN',
            amount: amount,
            address: toAddress,
            table: 'address_all',
            field: 'bep20-usdt',
            chainID: chainID,
            decimals: data.decimals,
            contractAddress: data.contractAddress,
            abi: data.abi,

        }, {
            attempts: 20,
            backoff: {
                type: 'exponential',
                delay: 1000,
            }
        });

        if (amount > 0) {

            let receipt = await provider.getTransactionReceipt(data.transactionHash);

            if (receipt && receipt.status == true) {

                const toInfo = JSON.parse(toHas);

                let find = await knex(toInfo.hashTable).where({
                    txid: data.transactionHash
                });
                if (find.length == 0) {
                    await knex(toInfo.hashTable).insert({
                        chainId: chainID,
                        sender: fromAddress,
                        address: toAddress,
                        amount: amount,
                        txid: data.transactionHash,
                        ipn_url: toInfo.ipn_url,
                        label: toInfo.label,
                        currency: chain,
                        blockNumber: data.blockNumber
                    });
                } else {
                    console.log(`系统已存在${job.data.chain}`, data.transactionHash);
                }
            } else {
                console.error('未确认，重新执行', data.transactionHash);
                await checkLogs_bep20_usdtQueuePro.add(`${data.transactionHash}`, job.data, {
                    attempts: 10,
                    backoff: {
                        type: 'exponential',
                        delay: 1000,
                    },
                    removeOnFail: 1000,
                    removeOnComplete: 2000
                });
            }
        } else {
            // console.error('不存...', job.data);
        }


    }

}, {
    connection: connection10,
    concurrency: 50
});


worker.on("completed", (job, result) => {
    // console.log(`Completed job on queue `, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("failed", (job, err) => {
    console.log(`Faille job on queue `, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'), job.data);
});

worker.on("error", (err) => {
    console.log(`error job on queue `, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('paused', (job) => {
    console.error(`paused job on `, job, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('resumed', (job) => {
    console.error(`resumed job on `, job, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('closed', () => {
    console.error(`worker  已退出`);
});


const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            console.error(`process.on ${type}`, err, origin);
            await worker.close();
            process.exit(0)
        } catch (_) {
            process.exit(1)
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.error('process.once', type);
        try {
            await worker.close();
        } finally {
            process.kill(process.pid, type)
        }
    })
});