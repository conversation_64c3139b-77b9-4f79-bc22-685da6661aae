const connection = require('../config/redis_config')(4);

const moment = require('moment');

const {
    Queue,
    Worker,
} = require('bullmq');


const {
    ethers
} = require("ethers");

const {
    url
} = require("../config/rpcUrl_config");

const provider = new ethers.providers.StaticJsonRpcProvider(url);


const QueueName = "updateApeBalanceQueuePro";


const mysqlConfig = require('../config/mysqlConfig');

const knex = require('knex')({
    client: 'mysql',
    connection: {
        host: mysqlConfig.host,
        user: mysqlConfig.user,
        port: mysqlConfig.port,
        password: mysqlConfig.password,
        database: mysqlConfig.database
    }
});

const worker = new Worker(QueueName, async (job) => {

    let address = job.data.address;
    let table = job.data.addressTable;
    let field = job.data.field;
    let balance = await provider.getBalance(address);

    console.log('job.data',job.data);

    balance = ethers.utils.formatEther(balance);
    if (balance) {
        console.log(address, balance);
        await knex(table).where({
            address: address
        }).update({
            [field]: balance
        });

    }


}, {
    connection: connection,
    concurrency: 10
});


worker.on("completed", (job, result) => {
    console.log(`Completed job on queue ${QueueName}`, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("failed", (job, err) => {
    console.log(`Faille job on queue ${QueueName}`, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("error", (err) => {
    console.log(`error job on queue ${QueueName}`, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('paused', (job) => {
    console.error(`paused job on queue ${QueueName}`, job, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('resumed', (job) => {
    console.error(`resumed job on queue ${QueueName}`, job, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('closed', () => {
    console.error(`worker ${QueueName} 已退出`);
});

const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.log(`process.on ${type}`)
        try {

            await worker.close();
            await scheduler.close();
            process.exit(0);
        } catch (err) {
            process.exit(1);
        }
    })
});