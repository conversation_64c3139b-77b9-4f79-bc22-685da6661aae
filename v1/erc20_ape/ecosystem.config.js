const {
  calcPath,
  getEnvVariables
} = require('./helpers');

module.exports = {
  apps: [

    {
      name: "v1/erc20-ape/getLogs",
      cwd: calcPath('src/workers'),
      script: "getLogs.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/erc20-ape/addRepeatJob",
      cwd: calcPath('src/workers'),
      script: "addRepeatJob.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/erc20-ape/checkLogs_erc20_ape_data",
      cwd: calcPath('src/workers'),
      script: "checkLogs_erc20_ape_data.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/erc20-ape/incrNum",
      cwd: calc<PERSON>ath('src/workers'),
      script: "incrNum.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/erc20-ape/updateTokenBalance",
      cwd: calcPath('src/workers'),
      script: "updateTokenBalance.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/erc20-ape/ui",
      cwd: calcPath('src'),
      script: "ui.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    }

  ]
}