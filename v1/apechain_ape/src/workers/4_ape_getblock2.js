const {
    ethers
} = require("ethers");

const connection4 = require('../config/redis_config')(4);

const {
    Queue,
    Worker,
} = require('bullmq');

const {
    url
} = require("../config/rpcUrl_config");

const provider = new ethers.providers.StaticJsonRpcProvider(url);


const Redis = require("ioredis");
const redis = new Redis({
    ...connection4,
    maxRetriesPerRequest: null
});

let lastnum = 0;

// 定时获取最新区块号
async function updateLatestBlockNumber() {
    try {
        lastnum = await provider.getBlockNumber();
    } catch (error) {
        console.error('获取最新区块号失败:', error);
    }
}

// 每10秒更新一次最新区块号
setInterval(updateLatestBlockNumber, 10000);

const workerGetBlockWithTransactionsQueue = new Queue("Ape_workerGetBlockWithTransactions", {
    connection: redis
});
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

async function start() {

    while (true) {

        try {
            await sleep(500);

            let block1 = Number(await redis.get('apechain_block_number2'));

            if (lastnum == 0) {
                lastnum = await provider.getBlockNumber();
            }
            // console.log('block1', block1);
            if (!block1) {
                block1 = block;
            }

            // 计算需要同步的区块范围
            const blockDiff = lastnum - block1;
            if (blockDiff <= 6) {
                continue;
            }

            // 批量添加区块任务，每次最多处理100个区块
            const batchSize = Math.min(blockDiff, 100);
            const tasks = [];

            for (let i = 0; i < batchSize; i++) {
                const currentBlock = block1 + i;
                tasks.push({
                    name: `numape_${currentBlock}`,
                    data: {
                        num: currentBlock.toString()
                    },
                    opts: {
                        attempts: 10,
                        backoff: {
                            type: 'exponential',
                            delay: 1000,
                        },
                        removeOnComplete: 1000,
                        removeOnFail: 1000
                    }
                });
            }

            // 批量添加任务到队列
            if (tasks.length > 0) {
                await workerGetBlockWithTransactionsQueue.addBulk(tasks);
                await redis.set('apechain_block_number2', block1 + tasks.length);
            }
        } catch (error) {
            console.error(error);
        }


    }
}

start();

const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.log(`process.on ${type}`)
        try {

            await workerGetBlockWithTransactionsQueue.close();

            process.exit(0);
        } catch (err) {
            process.exit(1);
        }
    })
});